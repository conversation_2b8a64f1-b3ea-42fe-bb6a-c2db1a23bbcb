"use client";

import React from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import LightRays from "./LightRays";

const HeroSection = () => {
  return (
    <section className="pt-32 pb-20 px-6 bg-black relative overflow-hidden">
      {/* Light Rays Background */}
      <div className="absolute inset-0 w-full h-full">
        {/* Primary white light rays */}
        <LightRays
          raysOrigin="top-center"
          raysColor="#ffffff"
          raysSpeed={1.5}
          lightSpread={0.4}
          rayLength={2.2}
          followMouse={true}
          mouseInfluence={0.15}
          noiseAmount={0.08}
          distortion={0.04}
          fadeDistance={1.2}
          saturation={1.0}
          pulsating={true}
          className="hero-light-rays"
        />

        {/* Secondary blue accent rays */}
        <LightRays
          raysOrigin="top-left"
          raysColor="#60a5fa"
          raysSpeed={0.8}
          lightSpread={0.6}
          rayLength={1.8}
          followMouse={true}
          mouseInfluence={0.1}
          noiseAmount={0.12}
          distortion={0.06}
          fadeDistance={0.9}
          saturation={0.8}
          className="hero-accent-rays"
        />

        {/* Tertiary purple accent rays */}
        <LightRays
          raysOrigin="top-right"
          raysColor="#a78bfa"
          raysSpeed={1.1}
          lightSpread={0.5}
          rayLength={1.6}
          followMouse={true}
          mouseInfluence={0.12}
          noiseAmount={0.1}
          distortion={0.05}
          fadeDistance={0.8}
          saturation={0.7}
          className="hero-purple-rays"
        />
      </div>

      {/* Subtle glow overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-white/5 via-transparent to-transparent pointer-events-none z-5"></div>

      <div className="container mx-auto max-w-6xl relative z-10">
        <div className="text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-6"
          >
            <span className="text-sm font-medium text-gray-400 bg-gray-800 px-3 py-1 rounded-full">
              G-Insight - Монголын тэргүүлэгч зах зээлийн судалгааны компани
            </span>
          </motion.div>

          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight"
            style={{
              textShadow: '0 0 20px rgba(255, 255, 255, 0.3), 0 0 40px rgba(255, 255, 255, 0.1)'
            }}
          >
            Зах зээлийн судалгаа
            <br />
            <span className="text-gray-400" style={{
              textShadow: '0 0 15px rgba(156, 163, 175, 0.4)'
            }}>мэргэжлийн түвшинд</span>
          </motion.h1>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-xl text-gray-300 max-w-3xl mx-auto mb-8 leading-relaxed"
            style={{
              textShadow: '0 0 10px rgba(209, 213, 219, 0.2)'
            }}
          >
            G-Insight нь таны бизнесийн өсөлтөд зориулсан найдвартай зах зээлийн судалгаа,
            хэрэглэгчийн зан төлөв, брэндийн танигдсан байдлын шинжилгээ үйлчилгээ үзүүлдэг.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          >
            <Button
              size="lg"
              className="bg-white text-black hover:bg-gray-200 rounded-lg px-8 py-3 text-base font-medium shadow-lg hover:shadow-xl transition-all duration-300"
              style={{
                boxShadow: '0 0 20px rgba(255, 255, 255, 0.3), 0 4px 15px rgba(0, 0, 0, 0.2)'
              }}
            >
              Эхлэх
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="border-gray-600 text-gray-300 hover:bg-gray-800 hover:border-gray-500 rounded-lg px-8 py-3 text-base font-medium transition-all duration-300"
              style={{
                boxShadow: '0 0 15px rgba(156, 163, 175, 0.2)'
              }}
            >
              Үнэ харах
            </Button>
          </motion.div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="mt-16"
          >
            <div className="relative max-w-4xl mx-auto">
              <div className="bg-gray-900 rounded-2xl shadow-2xl border border-gray-700 overflow-hidden">
                <div className="bg-gray-800 px-6 py-4 border-b border-gray-700">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  </div>
                </div>
                <div className="p-8">
                  <div className="grid grid-cols-3 gap-6 mb-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-white mb-2">500+</div>
                      <div className="text-sm text-gray-400">Амжилттай төсөл</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-white mb-2">15+</div>
                      <div className="text-sm text-gray-400">Жилийн туршлага</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-white mb-2">200+</div>
                      <div className="text-sm text-gray-400">Итгэмжлэгдсэн үйлчлүүлэгч</div>
                    </div>
                  </div>
                  <div className="h-32 bg-gradient-to-r from-blue-900 to-purple-900 rounded-lg flex items-center justify-center">
                    <span className="text-gray-300 font-medium">Судалгааны дүн шинжилгээ</span>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
