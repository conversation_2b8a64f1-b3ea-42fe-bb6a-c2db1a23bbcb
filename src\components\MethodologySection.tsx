"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Progress } from "@/components/ui/progress";
import { 
  Phone, 
  Tablet, 
  MessageSquare, 
  Globe, 
  MapPin,
  Video,
  Users,
  Eye,
  TestTube,
  Home,
  Shield,
  CheckCircle,
  BarChart3,
  FileText
} from "lucide-react";

const MethodologySection = () => {
  const [activeTab, setActiveTab] = useState("quantitative");

  const quantitativeMethods = [
    {
      icon: <Phone className="w-6 h-6" />,
      title: "CATI",
      description: "Мэргэшсэн дуудлагын төвийн операторууд утсаар судалгаа авах",
      color: "bg-blue-500"
    },
    {
      icon: <Tablet className="w-6 h-6" />,
      title: "CAPI",
      description: "Мэргэшсэн судлаачдын баг таблетаар судалгаа авах",
      color: "bg-green-500"
    },
    {
      icon: <MessageSquare className="w-6 h-6" />,
      title: "SMS",
      description: "Санамсаргүй дугаарууд руу мессеж илгээн судалгаа авах",
      color: "bg-purple-500"
    },
    {
      icon: <Globe className="w-6 h-6" />,
      title: "CAWI",
      description: "Онлайнаар судалгаа авах",
      color: "bg-orange-500"
    },
    {
      icon: <MapPin className="w-6 h-6" />,
      title: "CLT",
      description: "Тодорхой байршилд судалгаа авах",
      color: "bg-red-500"
    }
  ];

  const qualitativeMethods = [
    {
      icon: <Video className="w-6 h-6" />,
      title: "OFGD",
      description: "Онлайн хэлбэрээр фокус бүлгийн ярилцлага судалгаа авах",
      color: "bg-indigo-500"
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: "FGD",
      description: "Нүүр тулсан хэлбэрээр фокус бүлгийн ярилцлага судалгаа авах",
      color: "bg-teal-500"
    },
    {
      icon: <Eye className="w-6 h-6" />,
      title: "IDI",
      description: "Биечилсэн хэлбэрээр чанарын ярилцлагын судалгаа авах",
      color: "bg-pink-500"
    },
    {
      icon: <TestTube className="w-6 h-6" />,
      title: "Blind Test",
      description: "Хэрэглэгчид бүтээгдэхүүнийг харуулалгүйгээр туршиж үзүүлэх байдлаар судалгаа авах",
      color: "bg-cyan-500"
    },
    {
      icon: <Home className="w-6 h-6" />,
      title: "IHUT",
      description: "Хэрэглэгчээр гэрт нь хэрэглүүлэх байдлаар судалгаа авах",
      color: "bg-amber-500"
    }
  ];

  const qualitySteps = [
    {
      icon: <FileText className="w-8 h-8" />,
      title: "Төлөвлөлтийн шат",
      description: "Судалгааны зорилго, асуулгыг сайтар тодорхойлох, Туршилтын судалгаа (Pilot Test) хийх",
      color: "bg-blue-500"
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: "Хэрэгжилтийн шат",
      description: "Судлаачдын сургалт, зааварчилгаа өгөх, Өгөгдөл цуглуулах явцын мониторинг хийх",
      color: "bg-green-500"
    },
    {
      icon: <BarChart3 className="w-8 h-8" />,
      title: "Боловсруулалтын шат",
      description: "Дата цэвэрлэх, кодлох, Статистик шинжилгээ хийх",
      color: "bg-purple-500"
    },
    {
      icon: <CheckCircle className="w-8 h-8" />,
      title: "Хяналтын шат",
      description: "Хариултуудын баталгаажуулалт (Validation check), Туршилтын судалгаа болон бодит өгөгдлийг харьцуулах, Гуравдагч этгээдээр хяналт хийлгэх",
      color: "bg-orange-500"
    }
  ];

  const ethicsPoints = [
    {
      icon: <Shield className="w-6 h-6" />,
      title: "Судалгаанд оролцогчийн эрх ашгийг хамгаалах",
      description: "Судалгаанд оролцогчдын сайн дурын оролцоо, судалгааны талаарх мэдээлэл, хувийн мэдээллийг чандлан хадгалах зэргээр эрх ашгийг хамгаалах"
    },
    {
      icon: <CheckCircle className="w-6 h-6" />,
      title: "ХАРААТ БУС БАЙДАЛ, ШУДАРГА БАЙДАЛ",
      description: "Судалгааг хөндлөнгийн зүгээс хийж гүйцэтгэж байгаа тул ямар нэгэн гадны нөлөөлөлд автах болоод судалгааны үр дүнд гуйвуулалгүйгээр үнэнчээр захиалагчид мэдээлэх, тайлагнах"
    },
    {
      icon: <Eye className="w-6 h-6" />,
      title: "ИЛ ТОД, НЭЭЛТТЭЙ БАЙДАЛ",
      description: "Судалгааны явц, аргачлал, явцын нөхцөл байдал, үр дүнг тодорхой тайлбарлаж, нээлттэй мэдээллэж байх"
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-b from-gray-50 to-white">
      <div className="container mx-auto px-4">
        {/* Research Methods */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <Badge variant="outline" className="mb-4">
            Судалгааны арга зүй
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            МЭДЭЭЛЭЛ ЦУГЛУУЛАЛТЫН АРГАЧЛАЛ
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Бид олон төрлийн орчин үеийн аргачлалыг ашиглан чанартай, найдвартай мэдээлэл цуглуулдаг
          </p>
        </motion.div>

        {/* Method Tabs */}
        <div className="flex justify-center mb-12">
          <div className="bg-gray-100 p-1 rounded-lg">
            <button
              onClick={() => setActiveTab("quantitative")}
              className={`px-6 py-3 rounded-md font-semibold transition-all ${
                activeTab === "quantitative"
                  ? "bg-white text-blue-600 shadow-md"
                  : "text-gray-600 hover:text-gray-800"
              }`}
            >
              Тоон судалгаа
            </button>
            <button
              onClick={() => setActiveTab("qualitative")}
              className={`px-6 py-3 rounded-md font-semibold transition-all ${
                activeTab === "qualitative"
                  ? "bg-white text-blue-600 shadow-md"
                  : "text-gray-600 hover:text-gray-800"
              }`}
            >
              Чанарын судалгаа
            </button>
          </div>
        </div>

        {/* Method Cards */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-20"
        >
          {(activeTab === "quantitative" ? quantitativeMethods : qualitativeMethods).map((method, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              whileHover={{ y: -5 }}
            >
              <Card className="h-full glass border border-white/20 hover:shadow-glow transition-all duration-500 hover-lift group overflow-hidden relative">
                <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <CardHeader className="relative z-10">
                  <div className={`w-12 h-12 rounded-xl ${method.color} flex items-center justify-center text-white mb-4 shadow-glow group-hover:shadow-glow-lg transition-all duration-300 group-hover:scale-110`}>
                    {method.icon}
                  </div>
                  <CardTitle className="text-xl font-bold bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent">
                    {method.title}
                  </CardTitle>
                </CardHeader>
                <CardContent className="relative z-10">
                  <CardDescription className="text-base text-muted-foreground group-hover:text-foreground transition-colors duration-300">
                    {method.description}
                  </CardDescription>
                  <Progress value={75 + index * 5} className="mt-4 h-2" />
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Quality Control */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            МЭДЭЭЛЭЛ ЦУГЛУУЛАЛТЫН ЧАНАРЫН ХЯНАЛТ
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Манай байгууллага судалгааны чанарт дээд зэргээр анхаарч ажилладаг бөгөөд 
            үүний тулд дараах 4 шатны хүрээнд чанарын хяналтыг явуулан шалгаж ажилладаг
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-20">
          {qualitySteps.map((step, index) => (
            <motion.div
              key={step.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="text-center group"
            >
              <div className="glass border border-white/20 rounded-2xl p-6 hover:shadow-glow transition-all duration-500 hover-lift">
                <div className={`w-16 h-16 rounded-full gradient-primary flex items-center justify-center text-white mx-auto mb-4 shadow-glow group-hover:shadow-glow-lg transition-all duration-300 group-hover:scale-110`}>
                  {step.icon}
                </div>
                <h3 className="text-xl font-bold mb-4 bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">{step.title}</h3>
                <p className="text-muted-foreground group-hover:text-foreground transition-colors duration-300">{step.description}</p>
                <Progress value={80 + index * 5} className="mt-4 h-2" />
              </div>
            </motion.div>
          ))}
        </div>

        {/* Ethics Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            СУДАЛГААНЫ ЗАРЧИМ, ЁС ЗҮЙ
          </h2>
          <p className="text-xl text-muted-foreground max-w-4xl mx-auto">
            Бид судалгааны мэдээлэл цуглуулалт, дата анализ, тайлан боловсруулах, бичих болон захиалагчид тайлагнах, 
            тайланг хүлээлгэн өгөх зэрэг судалгааны бүхий л үйл явцад дараах хэм хэмжээ, эрхэмлэх үнэт зүйлсийг баримталдаг
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {ethicsPoints.map((point, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
              viewport={{ once: true }}
            >
              <Card className="h-full">
                <CardHeader>
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center text-blue-600">
                      {point.icon}
                    </div>
                    <CardTitle className="text-lg font-bold">
                      {point.title}
                    </CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base leading-relaxed">
                    {point.description}
                  </CardDescription>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* ISO Certification */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl p-8 text-white text-center"
        >
          <div className="max-w-4xl mx-auto">
            <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-blue-600 font-bold text-2xl">ISO</span>
            </div>
            <h3 className="text-3xl font-bold mb-4">ISO 20252 Стандарт</h3>
            <p className="text-xl mb-6">
              ISO 20252 бол зах зээлийн болон нийгмийн судалгаа, санал асуулга, дата анализ хийх байгууллагуудад 
              зориулсан олон улсын стандарт юм. Тус стандарт нь судалгааны үйл ажиллагааг чанартай, найдвартай, 
              ёс зүйн хэм хэмжээнд нийцүүлэн гүйцэтгэхэд чиглэсэн удирдамж өгдөг.
            </p>
            <p className="text-lg">
              Энэхүү стандартыг Инсайт судалгааны компани 2021 оноос эхлэн холбогдох журмын дагуу 
              үйл ажиллагаандаа бүрэн нэвтрүүлж судалгааны төлөвлөлт, хэрэгжилт, хяналтын түвшинд хэрэгжүүлэн ажиллаж байна.
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default MethodologySection;
