"use client";

import React from "react";
import { motion } from "framer-motion";
import { Star } from "lucide-react";

const TestimonialsSection = () => {
  const testimonials = [
    {
      name: "Б.Батбаяр",
      position: "Маркетингийн захирал",
      company: "Монго<PERSON> Банк",
      image: "/api/placeholder/80/80",
      rating: 5,
      quote: "G-Insight-ийн судалгааны үр дүн маш нарийвчлалтай бөгөөд манай маркетингийн стратегийг боловсруулахад ихээхэн тусалсан. Мэргэжлийн түвшин өндөр."
    },
    {
      name: "С.Сарангэрэл",
      position: "Гүйцэтгэх захирал",
      company: "Хаан Банк",
      image: "/api/placeholder/80/80",
      rating: 5,
      quote: "Хэрэглэгчийн сэтгэл ханамжийн судалгаа хийлгэснээр манай үйлчилгээний чанарыг сайжруулах чухал мэдээлэл олж авсан. Үр дүн гайхалтай."
    },
    {
      name: "Д.Дорж",
      position: "Борд гишүүн",
      company: "Мобиком Корпораци",
      image: "/api/placeholder/80/80",
      rating: 5,
      quote: "Зах зээлийн судалгааны тайлан маш ойлгомжтой, практик зөвлөмжүүд өгсөн. Шинэ бүтээгдэхүүн гаргахад чухал үүрэг гүйцэтгэсэн."
    },
    {
      name: "Ж.Жавхлан",
      position: "Маркетингийн менежер",
      company: "Унител",
      image: "/api/placeholder/80/80",
      rating: 5,
      quote: "Брэндийн танигдсан байдлын судалгаа хийлгэснээр өрсөлдөгчтэйгээ харьцуулсан байр сууриа тодорхой мэдэж авсан. Маш их баярлалаа."
    },
    {
      name: "Т.Төмөрбаатар",
      position: "Худалдааны захирал",
      company: "Эрдэнэс Монгол",
      image: "/api/placeholder/80/80",
      rating: 5,
      quote: "G-Insight-ийн баг маш мэргэжлийн түвшинд ажилладаг. Хугацаандаа чанартай үр дүн гаргаж өгсөн. Дахин хамтран ажиллахыг хүсч байна."
    },
    {
      name: "О.Оюунчимэг",
      position: "Стратегийн захирал",
      company: "Говь",
      image: "/api/placeholder/80/80",
      rating: 5,
      quote: "Хэрэглэгчийн зан төлөвийн судалгаа хийлгэснээр зорилтот хэрэглэгчдээ илүү сайн ойлгож, маркетингийн зардлаа хэмнэж чадсан."
    }
  ];

  return (
    <section id="reviews" className="py-24 bg-black">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="mb-4"
          >
            <span className="text-sm font-medium text-gray-400 bg-gray-800 px-3 py-1 rounded-full">
              Үйлчлүүлэгчдийн сэтгэгдэл
            </span>
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
            className="text-4xl md:text-5xl font-bold text-white mb-6"
          >
            Үйлчлүүлэгчид
            <br />
            <span className="text-gray-400">юу хэлж байна вэ?</span>
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-xl text-gray-300 max-w-3xl mx-auto"
          >
            Монголын тэргүүлэгч компаниуд G-Insight-ийн үйлчилгээнд итгэж,
            амжилттай хамтран ажиллаж байна.
          </motion.p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={testimonial.name}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-gray-900 border border-gray-700 rounded-2xl p-6 hover:shadow-lg transition-all duration-300"
            >
              {/* Rating */}
              <div className="flex items-center mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star key={`${testimonial.name}-star-${i}`} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                ))}
              </div>

              {/* Quote */}
              <blockquote className="text-gray-300 mb-6 leading-relaxed">
                "{testimonial.quote}"
              </blockquote>

              {/* Author */}
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gray-700 rounded-full flex items-center justify-center mr-4">
                  <span className="text-gray-300 font-medium text-sm">
                    {testimonial.name.charAt(0)}
                  </span>
                </div>
                <div>
                  <div className="font-semibold text-white">{testimonial.name}</div>
                  <div className="text-sm text-gray-400">
                    {testimonial.position}, {testimonial.company}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="bg-gray-800 rounded-2xl p-8"
        >
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-white mb-2">98%</div>
              <div className="text-sm text-gray-400">Үйлчлүүлэгчийн сэтгэл ханамж</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-white mb-2">200+</div>
              <div className="text-sm text-gray-400">Амжилттай төсөл</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-white mb-2">15+</div>
              <div className="text-sm text-gray-400">Жилийн туршлага</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-white mb-2">24/7</div>
              <div className="text-sm text-gray-400">Дэмжлэг үйлчилгээ</div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
