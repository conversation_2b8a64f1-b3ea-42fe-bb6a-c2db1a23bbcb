"use client";

import React from "react";
import Link from "next/link";
import {
  MapPin,
  Phone,
  Mail
} from "lucide-react";

const Footer = () => {

  return (
    <footer className="bg-black border-t border-gray-800">
      <div className="container mx-auto px-6 py-16">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
          {/* Company Info */}
          <div className="md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
                <span className="text-black font-bold text-sm">G</span>
              </div>
              <span className="text-xl font-semibold text-white">G-Insight</span>
            </div>
            <p className="text-gray-300 mb-6 max-w-md">
              Монголын тэргүүлэгч зах зээлийн судалгааны компани.
              Мэргэжлийн судалгааны үйлчилгээгээр танай бизнесийн амжилтанд хувь нэмэр оруулна.
            </p>
            <div className="space-y-2">
              <div className="flex items-center text-gray-400">
                <MapPin className="w-4 h-4 mr-2" />
                <span className="text-sm">Улаанбаатар хот, Сүхбаатар дүүрэг</span>
              </div>
              <div className="flex items-center text-gray-400">
                <Phone className="w-4 h-4 mr-2" />
                <span className="text-sm">+976 7777-7777</span>
              </div>
              <div className="flex items-center text-gray-400">
                <Mail className="w-4 h-4 mr-2" />
                <span className="text-sm"><EMAIL></span>
              </div>
            </div>
          </div>

          {/* Services */}
          <div>
            <h3 className="font-semibold text-white mb-4">Үйлчилгээ</h3>
            <ul className="space-y-2">
              <li><Link href="#features" className="text-gray-400 hover:text-white transition-colors text-sm">Брэндийн судалгаа</Link></li>
              <li><Link href="#features" className="text-gray-400 hover:text-white transition-colors text-sm">Хэрэглэгчийн зан төлөв</Link></li>
              <li><Link href="#features" className="text-gray-400 hover:text-white transition-colors text-sm">Сэтгэл ханамж</Link></li>
              <li><Link href="#features" className="text-gray-400 hover:text-white transition-colors text-sm">Зах зээлийн багтаамж</Link></li>
            </ul>
          </div>

          {/* Company */}
          <div>
            <h3 className="font-semibold text-white mb-4">Компани</h3>
            <ul className="space-y-2">
              <li><Link href="#" className="text-gray-400 hover:text-white transition-colors text-sm">Бидний тухай</Link></li>
              <li><Link href="#reviews" className="text-gray-400 hover:text-white transition-colors text-sm">Үйлчлүүлэгчид</Link></li>
              <li><Link href="#" className="text-gray-400 hover:text-white transition-colors text-sm">Карьер</Link></li>
              <li><Link href="#" className="text-gray-400 hover:text-white transition-colors text-sm">Холбоо барих</Link></li>
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-800 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-gray-400 text-sm">
              © 2024 G-Insight. Бүх эрх хуулиар хамгаалагдсан.
            </div>

            <div className="flex items-center space-x-6">
              <Link
                href="#"
                className="text-gray-400 hover:text-white transition-colors text-sm"
              >
                Нууцлалын бодлого
              </Link>
              <Link
                href="#"
                className="text-gray-400 hover:text-white transition-colors text-sm"
              >
                Үйлчилгээний нөхцөл
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
