"use client";

import React from "react";
import { motion } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { InfiniteMovingCards } from "@/components/ui/infinite-moving-cards";

const ClientLogosSection = () => {
  // Mock client data - in a real app, these would be actual client logos and testimonials
  const clients = [
    {
      name: "TDB Bank",
      logo: "TDB",
      testimonial: "G-Insight-ийн судалгааны үр дүн маш чанартай бөгөөд бидний бизнесийн шийдвэр гаргахад их тусалсан."
    },
    {
      name: "Golomt Bank",
      logo: "GB",
      testimonial: "Мэргэжлийн арга зүй, найдвартай үр дүнгээрээ бидний хамгийн найдвартай түнш юм."
    },
    {
      name: "Khan Bank",
      logo: "K<PERSON>",
      testimonial: "Хэрэглэгчийн сэтгэл ханамжийн судалгаа хийлгэснээр үйлчилгээний чанараа эрс сайжруулж чадсан."
    },
    {
      name: "State Bank",
      logo: "SB",
      testimonial: "Зах зээлийн судалгааны үр дүн нь бидний маркетингийн стратегийг боловсруулахад чухал үүрэг гүйцэтгэсэн."
    },
    {
      name: "Capitron Bank",
      logo: "CB",
      testimonial: "G-Insight-тай хамтран ажиллаж байгаадаа сэтгэл хангалуун байна. Үр дүн нь хүлээлтээс давсан."
    },
    {
      name: "Ard Insurance",
      logo: "AI",
      testimonial: "Брэндийн танигдсан байдлын судалгаа хийлгэснээр зах зээл дэх байр суурийг тодорхой мэдэж чадсан."
    }
  ];

  const testimonials = clients.map(client => ({
    quote: client.testimonial,
    name: client.name,
    title: "Үйлчлүүлэгч"
  }));

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <Badge variant="outline" className="mb-4">
            Бидний үйлчлүүлэгчид
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            ХАРИЛЦАГЧ БАЙГУУЛЛАГУУД
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Монголын томоохон банк, даатгалын компани, үйлдвэрлэлийн байгууллагууд 
            бидний мэргэжлийн үйлчилгээнд итгэж, тогтмол хамтран ажилладаг
          </p>
        </motion.div>

        {/* Client Logos Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center">
            {clients.map((client, index) => (
              <motion.div
                key={client.name}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.15, y: -5 }}
                className="flex items-center justify-center group"
              >
                <div className="w-24 h-24 glass border border-white/20 rounded-xl flex items-center justify-center shadow-soft hover:shadow-glow transition-all duration-300 hover-lift group-hover:bg-white/20">
                  <span className="text-2xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                    {client.logo}
                  </span>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Testimonials Carousel */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          <h3 className="text-2xl font-bold text-center mb-8 bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
            Үйлчлүүлэгчдийн сэтгэгдэл
          </h3>
          <div className="glass border border-white/20 rounded-2xl p-4 shadow-soft">
            <InfiniteMovingCards
              items={testimonials}
              direction="right"
              speed="slow"
              className="py-8"
            />
          </div>
        </motion.div>

        {/* Additional Client Categories */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-3 gap-8"
        >
          <div className="text-center p-6 bg-blue-50 rounded-xl">
            <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-white text-2xl font-bold">B</span>
            </div>
            <h4 className="text-xl font-bold mb-2">Банк санхүүгийн салбар</h4>
            <p className="text-muted-foreground">
              Монголын тэргүүлэгч банкууд бидний үйлчилгээг тогтмол ашигладаг
            </p>
          </div>

          <div className="text-center p-6 bg-green-50 rounded-xl">
            <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-white text-2xl font-bold">I</span>
            </div>
            <h4 className="text-xl font-bold mb-2">Даатгалын компаниуд</h4>
            <p className="text-muted-foreground">
              Даатгалын зах зээлийн судалгаанд тусгайлан мэргэшсэн
            </p>
          </div>

          <div className="text-center p-6 bg-purple-50 rounded-xl">
            <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-white text-2xl font-bold">C</span>
            </div>
            <h4 className="text-xl font-bold mb-2">Хэрэглээний бараа</h4>
            <p className="text-muted-foreground">
              FMCG компаниудын брэнд, бүтээгдэхүүний судалгаа
            </p>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <p className="text-lg text-muted-foreground mb-6">
            Танай байгууллага ч бидний амжилттай үйлчлүүлэгчдийн нэг болж, 
            мэргэжлийн судалгааны үйлчилгээг авахыг хүсвэл
          </p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold text-lg transition-colors"
          >
            Холбоо барих
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
};

export default ClientLogosSection;
