"use client"

import { ComponentPropsWithoutRef, useEffect, useRef, useState } from "react"
import {
  animate,
  motion,
  useMotionTemplate,
  useMotionValue,
} from "framer-motion"
import {
  LayoutDashboard,
  Rocket,
  Lightbulb,
} from "lucide-react";

const tabs = [
  {
    icon: LayoutDashboard,
    title: "Зах зээл",
    description: "Зах зээлийн хэрэгцээ, өрсөлдөөн, боломжийг дэлгэрэнгүй судлах үйлчилгээ.",
    isNew: false,
    backgroundPositionX: 0,
    backgroundPositionY: 0,
    backgroundSizeX: 150,
  },
  {
    icon: Rocket,
    title: "Хэрэглэгч",
    description: "Хэрэглэгчийн сонголт, зан төлөв, хэрэгцээг судлах мэргэжлийн шинжилгээ.",
    isNew: false,
    backgroundPositionX: 80,
    backgroundPositionY: 90,
    backgroundSizeX: 135,
  },
  {
    icon: Lightbulb,
    title: "Брэнд",
    description: "Брэндийн танигдсан байдал, нөлөөллийг хэмжих дэлгэрэнгүй судалгаа.",
    isNew: false,
    backgroundPositionX: 120,
    backgroundPositionY: 30,
    backgroundSizeX: 170,
  },
];

const FeatureTab = (
  props: (typeof tabs)[number] &
    ComponentPropsWithoutRef<"button"> & { selected: boolean }
) => {
  const tabRef = useRef<HTMLButtonElement>(null)

  const xPercent = useMotionValue(100)
  const yPercent = useMotionValue(0)
  const maskImage = useMotionTemplate`radial-gradient(100px 50px at ${xPercent}% ${yPercent}%, black, transparent)`
  
  useEffect(() => {
    if (!tabRef.current || !props.selected) return

    xPercent.set(0)
    yPercent.set(0)
    const rect = tabRef.current.getBoundingClientRect()
    const { height, width } = rect
    const circumference = height * 2 + width * 2
    const times = [
      0,
      width / circumference,
      (width + height) / circumference,
      (width * 2 + height) / circumference,
      1,
    ]
    
    animate(xPercent, [0, 100, 100, 0, 0], {
      duration: 4,
      times,
      ease: "linear",
      repeat: Infinity,
      repeatType: "loop",
    })
    animate(yPercent, [0, 0, 100, 100, 0], {
      times,
      duration: 4,
      ease: "linear",
      repeat: Infinity,
      repeatType: "loop",
    })
  }, [props.selected, xPercent, yPercent])

  return (
    <button
      ref={tabRef}
      className="border border-gray-600 hover:border-gray-500 bg-black/80 backdrop-blur-sm rounded-lg flex items-center gap-2 px-3 py-2 relative transition-all duration-300 hover:bg-black/90 text-left"
      onClick={props.onClick}
    >
      {props.selected && (
        <motion.div
          style={{
            maskImage,
          }}
          className="absolute inset-0 -m-px border border-blue-400 rounded-lg"
        ></motion.div>
      )}

      <div className="h-8 w-8 rounded-lg bg-black flex items-center justify-center text-gray-300">
        <props.icon className="w-4 h-4" />
      </div>
      <div className="flex-1">
        <div className="text-sm font-medium text-white">{props.title}</div>
      </div>
      {props.isNew && (
        <div className="bg-blue-500 rounded-lg text-white px-2 py-1 font-semibold text-xs">
          шинэ
        </div>
      )}
    </button>
  )
}

export default function HeroFeaturedTabs() {
  const [selectedTab, setSelectedTab] = useState(0)

  const backgroundPositionX = useMotionValue(tabs[0].backgroundPositionX)
  const backgroundPositionY = useMotionValue(tabs[0].backgroundPositionY)
  const backgroundSizeX = useMotionValue(tabs[0].backgroundSizeX)

  const backgroundPosition = useMotionTemplate`${backgroundPositionX}% ${backgroundPositionY}%`
  const backgroundSize = useMotionTemplate`${backgroundSizeX}% auto`

  const handleSelectTab = (index: number) => {
    setSelectedTab(index)

    animate(
      backgroundSizeX,
      [backgroundSizeX.get(), 100, tabs[index].backgroundSizeX],
      {
        duration: 2,
        ease: "easeInOut",
      }
    )
    animate(
      backgroundPositionX,
      [backgroundPositionX.get(), 100, tabs[index].backgroundPositionX],
      {
        duration: 2,
        ease: "easeInOut",
      }
    )
    animate(
      backgroundPositionY,
      [backgroundPositionY.get(), 100, tabs[index].backgroundPositionY],
      {
        duration: 2,
        ease: "easeInOut",
      }
    )
  }
  
  return (
    <div className="w-full max-w-5xl mx-auto">
      <div className="flex flex-col sm:flex-row gap-3 mb-8 justify-center">
        {tabs.map((tab, tabIndex) => (
          <FeatureTab
            {...tab}
            selected={selectedTab === tabIndex}
            onClick={() => handleSelectTab(tabIndex)}
            key={tab.title}
          />
        ))}
      </div>
      <div className="border-2 border-gray-700 bg-black/30 backdrop-blur-sm rounded-2xl p-4">
        <motion.div
          className="aspect-[16/10] bg-cover border border-gray-700 rounded-lg min-h-[400px]"
          style={{
            backgroundPosition,
            backgroundSize,
            backgroundImage: `url(https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=1200&h=600&fit=crop&crop=center)`,
          }}
        ></motion.div>
      </div>
    </div>
  )
}
