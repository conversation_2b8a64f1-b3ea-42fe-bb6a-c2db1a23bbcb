# G-Insight Landing Page

A modern, responsive landing page for G-Insight, a leading market research company in Mongolia. Built with Next.js 15, TypeScript, Tailwind CSS, and enhanced with shadcn/ui and Aceternity UI components.

## 🚀 Features

- **Modern Design**: Clean, professional layout with smooth animations
- **Responsive**: Fully responsive design that works on all devices
- **Performance**: Optimized for speed with Next.js 15 and Turbopack
- **Animations**: Smooth animations using Framer Motion
- **Accessibility**: Built with accessibility in mind using Radix UI components
- **TypeScript**: Full TypeScript support for better development experience
- **SEO Ready**: Optimized for search engines

## 🛠 Tech Stack

- **Framework**: Next.js 15.4.5 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**:
  - shadcn/ui (Radix UI based)
  - Aceternity UI for advanced animations
- **Animations**: Framer Motion
- **Icons**: Lucide React
- **Development**: Turbopack for fast development

## 📦 Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd g-insight-landing
```

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🏗 Project Structure

```
src/
├── app/
│   ├── globals.css          # Global styles and Tailwind config
│   ├── layout.tsx           # Root layout
│   └── page.tsx             # Main landing page
├── components/
│   ├── ui/                  # Reusable UI components
│   ├── Header.tsx           # Navigation header
│   ├── HeroSection.tsx      # Hero section with animated text
│   ├── ServicesSection.tsx  # Research services grid
│   ├── StatisticsSection.tsx # Partnership statistics
│   ├── ClientLogosSection.tsx # Client testimonials
│   ├── MethodologySection.tsx # Research methodology
│   ├── Footer.tsx           # Footer with contact info
│   ├── ScrollProgress.tsx   # Scroll progress indicator
│   └── BackToTop.tsx        # Back to top button
└── lib/
    └── utils.ts             # Utility functions
```

## 🎨 Components Overview

### Research Services
- 9 different types of market research services
- Interactive cards with hover effects
- Responsive grid layout

### Company Statistics
- Animated counters showing achievements
- 150+ client organizations
- 500+ successful projects
- 85% client retention rate

### Methodology
- ISO 20252 certified processes
- Quality control at every stage
- Both quantitative and qualitative research methods

## 🚀 Deployment

### Vercel (Recommended)
```bash
npm run build
```

Deploy to Vercel by connecting your GitHub repository.

### Other Platforms
```bash
npm run build
npm start
```

## 📞 Contact

For questions about this project, please contact the G-Insight <NAME_EMAIL>.

---

Built with ❤️ for G-Insight Market Research Company
