"use client";

import { BorderBeam } from "@/components/ui/border-beam";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { TracingBeam } from "@/components/ui/tracing-beam";
import { motion } from "framer-motion";
import { ArrowRight, CheckCircle } from "lucide-react";
import LightRays from "./LightRays";

export default function Hero195() {
  const benefits = [
    "15+ жилийн туршлага",
    "500+ амжилттай төсөл",
    "98% үйлчлүүлэгчийн сэтгэл ханамж",
    "24/7 мэргэжлийн дэмжлэг"
  ];

  return (
    <section className="relative min-h-screen bg-black overflow-hidden">
      {/* Light Rays Background */}
      <div className="absolute inset-0 w-full h-full">
        {/* Primary white light rays */}
        <LightRays
          raysOrigin="top-center"
          raysColor="#ffffff"
          raysSpeed={1.5}
          lightSpread={0.4}
          rayLength={2.2}
          followMouse={true}
          mouseInfluence={0.15}
          noiseAmount={0.08}
          distortion={0.04}
          fadeDistance={1.2}
          saturation={1.0}
          pulsating={true}
          className="hero-light-rays"
        />

        {/* Secondary blue accent rays */}
        <LightRays
          raysOrigin="top-left"
          raysColor="#60a5fa"
          raysSpeed={0.8}
          lightSpread={0.6}
          rayLength={1.8}
          followMouse={true}
          mouseInfluence={0.1}
          noiseAmount={0.12}
          distortion={0.06}
          fadeDistance={0.9}
          saturation={0.8}
          className="hero-accent-rays"
        />

        {/* Tertiary purple accent rays */}
        <LightRays
          raysOrigin="top-right"
          raysColor="#a78bfa"
          raysSpeed={1.1}
          lightSpread={0.5}
          rayLength={1.6}
          followMouse={true}
          mouseInfluence={0.12}
          noiseAmount={0.1}
          distortion={0.05}
          fadeDistance={0.8}
          saturation={0.7}
          className="hero-purple-rays"
        />
      </div>

      {/* Background Lines */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0" style={{
          backgroundImage: `
            linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px),
            linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px'
        }} />
      </div>

      {/* Subtle glow overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-white/5 via-transparent to-transparent pointer-events-none z-5"></div>

      <TracingBeam className="px-6">
        <div className="relative z-10 pt-8 pb-20">
          <div className="container mx-auto max-w-7xl">
            
            {/* Hero Content */}
            <div className="text-center mb-16">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="mb-6"
              >
                <span className="text-sm font-medium text-gray-400 bg-gray-800 px-4 py-2 rounded-full border border-gray-700">
                  G-Insight - Монголын тэргүүлэгч зах зээлийн судалгааны компани
                </span>
              </motion.div>

              <motion.h1
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight"
              >
                Зах зээлийн судалгаа
                <br />
                <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                  мэргэжлийн түвшинд
                </span>
              </motion.h1>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="text-xl text-gray-300 max-w-3xl mx-auto mb-8 leading-relaxed"
              >
                G-Insight нь таны бизнесийн өсөлтөд зориулсан найдвартай зах зээлийн судалгаа, 
                хэрэглэгчийн зан төлөв, брэндийн танигдсан байдлын шинжилгээ үйлчилгээ үзүүлдэг.
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12"
              >
                <Button size="lg" className="bg-white text-black hover:bg-gray-200 rounded-lg px-8 py-3 text-base font-medium">
                  Эхлэх
                  <ArrowRight className="ml-2 w-4 h-4" />
                </Button>
                <Button variant="outline" size="lg" className="border-gray-600 text-gray-300 hover:bg-gray-800 rounded-lg px-8 py-3 text-base font-medium">
                  Үнэ харах
                </Button>
              </motion.div>

              {/* Benefits */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="flex flex-wrap justify-center gap-6 mb-16"
              >
                {benefits.map((benefit) => (
                  <div key={benefit} className="flex items-center text-gray-300">
                    <CheckCircle className="w-4 h-4 text-green-400 mr-2" />
                    <span className="text-sm">{benefit}</span>
                  </div>
                ))}
              </motion.div>
            </div>

            {/* Hero 195 Cards Section */}
            <motion.div
              initial={{ opacity: 0, y: 40 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              className="relative"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-7xl mx-auto">
                {/* Card 1 - Market Research */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.6 }}
                  className="relative group"
                >
                  <Card className="bg-gray-900/50 border-gray-700 backdrop-blur-sm relative overflow-hidden h-80 hover:scale-105 transition-transform duration-300">
                    <BorderBeam size={200} duration={15} delay={0} colorFrom="#60a5fa" colorTo="#a78bfa" />
                    <div className="absolute inset-0">
                      <img
                        src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop&crop=center"
                        alt="Market Research Analytics"
                        className="w-full h-full object-cover opacity-60"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent" />
                    </div>
                    <CardContent className="relative z-10 p-6 h-full flex flex-col justify-end">
                      <div className="text-white">
                        <h3 className="text-xl font-bold mb-2">Зах зээлийн судалгаа</h3>
                        <p className="text-gray-300 text-sm">Зах зээлийн хэмжээ, өрсөлдөөн, боломжийн талаарх дэлгэрэнгүй шинжилгээ</p>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>

                {/* Card 2 - Consumer Behavior */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.7 }}
                  className="relative group"
                >
                  <Card className="bg-gray-900/50 border-gray-700 backdrop-blur-sm relative overflow-hidden h-80 hover:scale-105 transition-transform duration-300">
                    <BorderBeam size={200} duration={18} delay={3} colorFrom="#a78bfa" colorTo="#60a5fa" />
                    <div className="absolute inset-0">
                      <img
                        src="https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=400&h=300&fit=crop&crop=center"
                        alt="Consumer Behavior Analysis"
                        className="w-full h-full object-cover opacity-60"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent" />
                    </div>
                    <CardContent className="relative z-10 p-6 h-full flex flex-col justify-end">
                      <div className="text-white">
                        <h3 className="text-xl font-bold mb-2">Хэрэглэгчийн зан төлөв</h3>
                        <p className="text-gray-300 text-sm">Худалдан авалтын зан төлөв, сонголтын шалтгаан судлах</p>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>

                {/* Card 3 - Brand Recognition */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.8 }}
                  className="relative group"
                >
                  <Card className="bg-gray-900/50 border-gray-700 backdrop-blur-sm relative overflow-hidden h-80 hover:scale-105 transition-transform duration-300">
                    <BorderBeam size={200} duration={12} delay={6} colorFrom="#ffffff" colorTo="#60a5fa" />
                    <div className="absolute inset-0">
                      <img
                        src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=300&fit=crop&crop=center"
                        alt="Brand Recognition"
                        className="w-full h-full object-cover opacity-60"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent" />
                    </div>
                    <CardContent className="relative z-10 p-6 h-full flex flex-col justify-end">
                      <div className="text-white">
                        <h3 className="text-xl font-bold mb-2">Брэндийн танигдсан байдал</h3>
                        <p className="text-gray-300 text-sm">Брэндийн зах зээл дэх байр суурь, танигдсан байдлын судалгаа</p>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>

                {/* Card 4 - Customer Satisfaction */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.9 }}
                  className="relative group md:col-span-1 lg:col-span-1"
                >
                  <Card className="bg-gray-900/50 border-gray-700 backdrop-blur-sm relative overflow-hidden h-80 hover:scale-105 transition-transform duration-300">
                    <BorderBeam size={200} duration={20} delay={9} colorFrom="#a78bfa" colorTo="#ffffff" />
                    <div className="absolute inset-0">
                      <img
                        src="https://images.unsplash.com/photo-1559526324-4b87b5e36e44?w=400&h=300&fit=crop&crop=center"
                        alt="Customer Satisfaction"
                        className="w-full h-full object-cover opacity-60"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent" />
                    </div>
                    <CardContent className="relative z-10 p-6 h-full flex flex-col justify-end">
                      <div className="text-white">
                        <h3 className="text-xl font-bold mb-2">Хэрэглэгчийн сэтгэл ханамж</h3>
                        <p className="text-gray-300 text-sm">Үйлчилгээний чанар, бүтээгдэхүүний гүйцэтгэлийн үнэлгээ</p>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>

                {/* Card 5 - Data Analytics */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1.0 }}
                  className="relative group md:col-span-1 lg:col-span-2"
                >
                  <Card className="bg-gray-900/50 border-gray-700 backdrop-blur-sm relative overflow-hidden h-80 hover:scale-105 transition-transform duration-300">
                    <BorderBeam size={300} duration={14} delay={12} colorFrom="#60a5fa" colorTo="#ffffff" />
                    <div className="absolute inset-0">
                      <img
                        src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=300&fit=crop&crop=center"
                        alt="Data Analytics Dashboard"
                        className="w-full h-full object-cover opacity-60"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent" />
                    </div>
                    <CardContent className="relative z-10 p-6 h-full flex flex-col justify-end">
                      <div className="text-white">
                        <h3 className="text-2xl font-bold mb-3">Өгөгдлийн шинжилгээ ба тайлан</h3>
                        <p className="text-gray-300 text-base">Цуглуулсан өгөгдлийг боловсруулж, ойлгомжтой тайлан, зөвлөмж бэлтгэх үйлчилгээ. Бизнесийн шийдвэр гаргахад туслах дэлгэрэнгүй шинжилгээ.</p>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </TracingBeam>
    </section>
  );
}
