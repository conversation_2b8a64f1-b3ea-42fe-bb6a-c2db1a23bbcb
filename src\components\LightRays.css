.light-rays-container {
  width: 100%;
  height: 100%;
  position: relative;
  pointer-events: none;
  z-index: 3;
  overflow: hidden;
}

/* Enhanced lighting effects for hero section */
section:has(.hero-light-rays) {
  background: radial-gradient(ellipse at top center, rgba(255, 255, 255, 0.03) 0%, transparent 50%),
              radial-gradient(ellipse at top left, rgba(96, 165, 250, 0.02) 0%, transparent 40%),
              radial-gradient(ellipse at top right, rgba(167, 139, 250, 0.02) 0%, transparent 40%),
              #000000;
}

.hero-light-rays {
  opacity: 0.7;
  mix-blend-mode: screen;
  filter: blur(0.3px) brightness(1.2) contrast(1.1);
  animation: lightPulse 4s ease-in-out infinite alternate;
}

@keyframes lightPulse {
  0% {
    opacity: 0.6;
    filter: blur(0.3px) brightness(1.1) contrast(1.0);
  }
  100% {
    opacity: 0.8;
    filter: blur(0.2px) brightness(1.3) contrast(1.2);
  }
}

.hero-accent-rays {
  opacity: 0.4;
  mix-blend-mode: screen;
  filter: blur(0.4px) brightness(1.1);
  animation: accentPulse 6s ease-in-out infinite alternate;
}

.hero-purple-rays {
  opacity: 0.35;
  mix-blend-mode: screen;
  filter: blur(0.5px) brightness(1.0);
  animation: purplePulse 5s ease-in-out infinite alternate-reverse;
}

@keyframes accentPulse {
  0% {
    opacity: 0.3;
    filter: blur(0.5px) brightness(1.0);
  }
  100% {
    opacity: 0.5;
    filter: blur(0.3px) brightness(1.2);
  }
}

@keyframes purplePulse {
  0% {
    opacity: 0.25;
    filter: blur(0.6px) brightness(0.9);
  }
  100% {
    opacity: 0.45;
    filter: blur(0.4px) brightness(1.1);
  }
}
