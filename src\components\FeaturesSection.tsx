"use client";

import React from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { 
  BarChart3, 
  Users, 
  Target, 
  TrendingUp,
  Search,
  ShoppingCart,
  UserCheck,
  Package
} from "lucide-react";

const FeaturesSection = () => {
  const features = [
    {
      icon: <BarChart3 className="w-6 h-6" />,
      title: "Брэндийн танигдсан байдал",
      description: "Таны брэндийн зах зээл дэх байр суурь, өрсөлдөгчтэй харьцуулсан танигдсан байдлын судалгаа. Зорилтот хэрэглэгчдийн дунд брэндийн ойлголт, сэтгэгдлийг тодорхойлно.",
      image: "/api/placeholder/400/300"
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: "Хэрэглэгчийн зан төлөв",
      description: "Хэрэглэгчдийн худалдан авалтын зан төлөв, сонголтын шалтгаан, шийдвэр гаргах үйл явцын гүнзгий шинжилгээ. Зах зээлийн сегментчилэл, хэрэглэгчийн хэрэгцээ.",
      image: "/api/placeholder/400/300"
    },
    {
      icon: <Target className="w-6 h-6" />,
      title: "Хэрэглэгчийн сэтгэл ханамж",
      description: "Үйлчилгээний чанар, бүтээгдэхүүний гүйцэтгэл, харилцааны талаарх хэрэглэгчдийн сэтгэл ханамжийн түвшинг хэмжих судалгаа. NPS, CSAT үзүүлэлтүүд.",
      image: "/api/placeholder/400/300"
    },
    {
      icon: <TrendingUp className="w-6 h-6" />,
      title: "Зах зээлийн багтаамж",
      description: "Зах зээлийн хэмжээ, өсөлтийн боломж, зах зээлийн хувьцаа, өрсөлдөөний байдлын судалгаа. Шинэ бүтээгдэхүүн, үйлчилгээний зах зээлийн боломжийг үнэлэх.",
      image: "/api/placeholder/400/300"
    }
  ];

  return (
    <section id="features" className="py-24 bg-black">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="mb-4"
          >
            <span className="text-sm font-medium text-gray-400 bg-gray-800 px-3 py-1 rounded-full">
              Үйлчилгээний төрлүүд
            </span>
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
            className="text-4xl md:text-5xl font-bold text-white mb-6"
          >
            Зах зээлийн судалгааны
            <br />
            <span className="text-gray-400">иж бүрэн шийдэл</span>
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-xl text-gray-300 max-w-3xl mx-auto"
          >
            G-Insight нь таны бизнесийн өсөлтөд зориулсан олон төрлийн зах зээлийн судалгааны үйлчилгээ үзүүлж,
            мэдээллэд суурилсан шийдвэр гаргахад тань туслана.
          </motion.p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="bg-gray-900 border border-gray-700 rounded-2xl p-8 hover:shadow-xl transition-all duration-300">
                <div className="flex items-start space-x-4 mb-6">
                  <div className="w-12 h-12 bg-white rounded-xl flex items-center justify-center text-black group-hover:scale-110 transition-transform duration-300">
                    {feature.icon}
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl font-semibold text-white mb-2">
                      {feature.title}
                    </h3>
                  </div>
                </div>

                <p className="text-gray-300 mb-6 leading-relaxed">
                  {feature.description}
                </p>

                <div className="h-48 bg-gradient-to-br from-gray-800 to-gray-700 rounded-xl flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gray-900 rounded-full flex items-center justify-center mx-auto mb-3 shadow-sm text-white">
                      {feature.icon}
                    </div>
                    <span className="text-sm text-gray-400">Судалгааны дүн</span>
                  </div>
                </div>

                <Button
                  variant="outline"
                  className="w-full mt-6 border-gray-600 text-gray-300 hover:bg-gray-800"
                >
                  Дэлгэрэнгүй
                </Button>
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <h3 className="text-2xl font-semibold text-white mb-4">
            Бусад үйлчилгээнүүд
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl mx-auto mb-8">
            <div className="bg-gray-800 rounded-lg p-4 text-center">
              <UserCheck className="w-6 h-6 mx-auto mb-2 text-gray-300" />
              <span className="text-sm text-gray-300">Ажилчдын сэтгэл ханамж</span>
            </div>
            <div className="bg-gray-800 rounded-lg p-4 text-center">
              <Search className="w-6 h-6 mx-auto mb-2 text-gray-300" />
              <span className="text-sm text-gray-300">Нууц үйлчлүүлэгч</span>
            </div>
            <div className="bg-gray-800 rounded-lg p-4 text-center">
              <ShoppingCart className="w-6 h-6 mx-auto mb-2 text-gray-300" />
              <span className="text-sm text-gray-300">Үйлчилгээний цэг</span>
            </div>
            <div className="bg-gray-800 rounded-lg p-4 text-center">
              <Package className="w-6 h-6 mx-auto mb-2 text-gray-300" />
              <span className="text-sm text-gray-300">Бүтээгдэхүүний тест</span>
            </div>
          </div>

          <Button className="bg-white text-black hover:bg-gray-200 rounded-lg px-8">
            Бүх үйлчилгээг харах
          </Button>
        </motion.div>
      </div>
    </section>
  );
};

export default FeaturesSection;
