"use client";

import React, { useEffect, useState } from "react";
import { motion, useInView } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Building2, Trophy, RefreshCw, Star, Users } from "lucide-react";

const StatisticsSection = () => {
  const [counts, setCounts] = useState({
    clients: 0,
    projects: 0,
    retention: 0,
    brands: 0,
    surveys: 0
  });

  const ref = React.useRef(null);
  const isInView = useInView(ref, { once: true });

  const stats = [
    {
      icon: <Building2 className="w-8 h-8" />,
      value: 150,
      suffix: "+",
      label: "Харилцагч байгууллага",
      color: "text-blue-600"
    },
    {
      icon: <Trophy className="w-8 h-8" />,
      value: 500,
      suffix: "+",
      label: "Амжилттай төслийн тоо",
      color: "text-green-600"
    },
    {
      icon: <RefreshCw className="w-8 h-8" />,
      value: 85,
      suffix: "%",
      label: "Дахин хамтарч ажиллаж буй хувь",
      color: "text-purple-600"
    },
    {
      icon: <Star className="w-8 h-8" />,
      value: 200,
      suffix: "+",
      label: "Харилцагч брэнд",
      color: "text-orange-600"
    },
    {
      icon: <Users className="w-8 h-8" />,
      value: 50000,
      suffix: "+",
      label: "Судалгаа авсан харилцагчид/иргэд",
      color: "text-red-600"
    }
  ];

  useEffect(() => {
    if (isInView) {
      const timers = stats.map((stat, index) => {
        return setTimeout(() => {
          const duration = 2000; // 2 seconds
          const steps = 60;
          const increment = stat.value / steps;
          let current = 0;

          const timer = setInterval(() => {
            current += increment;
            if (current >= stat.value) {
              current = stat.value;
              clearInterval(timer);
            }

            setCounts(prev => ({
              ...prev,
              [Object.keys(prev)[index]]: Math.floor(current)
            }));
          }, duration / steps);

          return timer;
        }, index * 200); // Stagger the animations
      });

      return () => {
        timers.forEach(timer => clearTimeout(timer));
      };
    }
  }, [isInView]);

  return (
    <section className="py-20 bg-gradient-to-r from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <Badge variant="outline" className="mb-4">
            Бидний амжилт
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            ХАМТЫН АЖИЛЛАГААНЫ ХҮРЭЭ
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Олон жилийн туршлага, мэргэжлийн ур чадвар, найдвартай үйлчилгээгээрээ 
            олон байгууллагатай амжилттай хамтран ажиллаж байна
          </p>
        </motion.div>

        <div ref={ref} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, scale: 0.5 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{
                duration: 0.6,
                delay: index * 0.1,
                type: "spring",
                stiffness: 100
              }}
              viewport={{ once: true }}
              className="text-center group"
            >
              <motion.div className="glass border border-white/20 rounded-2xl p-6 hover:shadow-glow transition-all duration-500 hover-lift">
                <motion.div
                  whileHover={{ scale: 1.2, rotate: 10 }}
                  className={`inline-flex items-center justify-center w-16 h-16 rounded-full gradient-primary shadow-glow mb-4 text-white group-hover:shadow-glow-lg transition-all duration-300`}
                >
                  {stat.icon}
                </motion.div>

                <motion.div
                  className="text-4xl md:text-5xl font-bold mb-2"
                  initial={{ scale: 0 }}
                  whileInView={{ scale: 1 }}
                  transition={{
                    duration: 0.5,
                    delay: index * 0.1 + 0.3,
                    type: "spring",
                    stiffness: 200
                  }}
                  viewport={{ once: true }}
                >
                  <span className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                    {Object.values(counts)[index]}{stat.suffix}
                  </span>
                </motion.div>

                <p className="text-lg font-medium text-muted-foreground group-hover:text-foreground transition-colors duration-300">
                  {stat.label}
                </p>

                <Progress
                  value={(Object.values(counts)[index] / stat.value) * 100}
                  className="mt-3 h-2"
                />
              </motion.div>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="glass border border-white/20 rounded-2xl p-8 shadow-glow max-w-4xl mx-auto hover-lift">
            <h3 className="text-2xl font-bold mb-4 bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
              Танай байгууллага ч бидний амжилттай түншүүдийн нэг болоорой
            </h3>
            <p className="text-lg text-muted-foreground mb-6">
              Мэргэжлийн судалгааны үйлчилгээгээр танай бизнесийн өсөлтөд хувь нэмэр оруулахад бэлэн байна
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="gradient-primary hover:shadow-glow-lg text-white px-8 py-3 rounded-lg font-semibold text-lg transition-all duration-300 hover-lift"
            >
              Хамтран ажиллах
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default StatisticsSection;
