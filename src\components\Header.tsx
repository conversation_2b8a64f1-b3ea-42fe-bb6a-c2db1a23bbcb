"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Menu, X, Settings, BarChart3, DollarSign, MessageSquare } from "lucide-react";
import { NavBar } from "@/components/ui/tubelight-navbar";

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const navItems = [
    { name: "Үйлчилгээ", url: "#features", icon: Settings },
    { name: "Харьцуулалт", url: "#comparison", icon: BarChart3 },
    { name: "Үнэ", url: "#pricing", icon: DollarSign },
    { name: "Сэтгэгдэл", url: "#reviews", icon: MessageSquare }
  ];

  return (
    <>
      {/* Tubelight Navigation - positioned in header area */}
      <div className="hidden md:block">
        <NavBar
          items={navItems}
        />
      </div>

      <header className={`fixed top-0 left-0 right-0 z-20 transition-all duration-300 ${
        isScrolled
          ? "bg-black/80 backdrop-blur-md border-b border-gray-800"
          : "bg-transparent"
      }`}>
        <div className="container mx-auto px-6">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
                <span className="text-black font-bold text-sm">G</span>
              </div>
              <span className="text-xl font-semibold text-white">
                G-Insight
              </span>
            </Link>

            {/* Spacer for desktop to account for centered navbar */}
            <div className="hidden md:block w-8"></div>

          {/* Right side buttons */}
          <div className="flex items-center space-x-4">
            <Button className="hidden md:flex bg-white text-black hover:bg-gray-200 rounded-lg px-6">
              Эхлэх
            </Button>

            {/* Mobile menu */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden p-2 rounded-lg hover:bg-gray-800 transition-colors text-white"
            >
              {isMenuOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <Menu className="h-5 w-5" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden absolute top-16 left-0 right-0 bg-black border-b border-gray-800 shadow-lg z-40">
            <nav className="container mx-auto px-6 py-4">
              <div className="flex flex-col space-y-4">
                {navItems.map((item) => (
                  <Link
                    key={item.name}
                    href={item.url}
                    className="text-sm font-medium text-gray-300 hover:text-white transition-colors py-2"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {item.name}
                  </Link>
                ))}
                <Button className="mt-4 bg-white text-black hover:bg-gray-200 rounded-lg px-6">
                  Эхлэх
                </Button>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
    </>
  );
};

export default Header;
